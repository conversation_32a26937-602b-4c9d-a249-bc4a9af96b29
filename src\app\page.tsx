import { redirect } from 'next/navigation';
import { getRedirectPathAfterLogin } from '../services/user/role-service';
import { getCurrentUser } from '@/services/auth/actions/auth-actions';

export default async function RootPage() {
  const user = await getCurrentUser();

  if (user) {
    const redirectPath = await getRedirectPathAfterLogin(user.id);
    redirect(redirectPath);
  }

  redirect('/login');
}
